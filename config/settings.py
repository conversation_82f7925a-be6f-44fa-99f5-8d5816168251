"""Configuration settings for the YouTube to Facebook transformation system."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings and configuration."""
    
    # OpenRouter API Configuration
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # Model selection based on task complexity
    MODELS = {
        "content_extraction": os.getenv('CONTENT_EXTRACTION_MODEL', "anthropic/claude-3.5-sonnet"),
        "sentiment_analysis": os.getenv('SENTIMENT_ANALYSIS_MODEL', "anthropic/claude-3.5-sonnet"), 
        "thought_processing": os.getenv('THOUGHT_PROCESSING_MODEL', "openai/gpt-4-turbo"),
        "content_generation": os.getenv('CONTENT_GENERATION_MODEL', "anthropic/claude-3.5-sonnet"),
        "audio_transcription": os.getenv('AUDIO_TRANSCRIPTION_MODEL', "openai/whisper-large-v3")
    }
    
    # Content processing limits
    MAX_TRANSCRIPT_LENGTH = 50000  # characters
    MIN_TRANSCRIPT_LENGTH = 100    # characters
    MAX_FACEBOOK_POST_LENGTH = 2000  # characters
    MIN_FACEBOOK_POST_LENGTH = 300   # characters
    
    # Retry configuration
    MAX_RETRIES = 3
    RETRY_DELAY = 1  # seconds
    
    # Logging configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    @classmethod
    def validate(cls):
        """Validate that required settings are present."""
        if not cls.OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY is required. Please set it in your .env file.")
        
        return True
