#!/usr/bin/env python3
"""
Debug script to test transcript extraction directly.
"""

import ssl
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import TextFormatter
from colorama import init, Fore, Style

# Fix SSL certificate issues
ssl._create_default_https_context = ssl._create_unverified_context

# Initialize colorama
init()

def debug_transcript_extraction(video_id: str):
    """Debug transcript extraction for a specific video ID."""
    
    print(f"{Fore.BLUE}🔍 Debugging transcript extraction for video: {video_id}{Style.RESET_ALL}")
    
    try:
        # Get available transcripts
        print(f"{Fore.YELLOW}📋 Getting transcript list...{Style.RESET_ALL}")
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        # List available transcripts
        available_transcripts = list(transcript_list)
        print(f"{Fore.GREEN}✅ Found {len(available_transcripts)} available transcripts{Style.RESET_ALL}")
        
        for transcript in available_transcripts:
            print(f"   - {transcript.language} ({'auto-generated' if transcript.is_generated else 'manual'})")
        
        # Try to get English transcript first
        print(f"\n{Fore.YELLOW}🎯 Attempting to get English transcript...{Style.RESET_ALL}")
        
        transcript = None
        language = "unknown"
        
        try:
            transcript = transcript_list.find_transcript(['en', 'en-US'])
            language = "English"
            print(f"{Fore.GREEN}✅ Found English transcript{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.YELLOW}⚠️  English transcript not found: {str(e)}{Style.RESET_ALL}")
            
            # Get any available transcript
            if available_transcripts:
                transcript = available_transcripts[0]
                language = transcript.language
                print(f"{Fore.GREEN}✅ Using {language} transcript instead{Style.RESET_ALL}")
        
        if transcript:
            # Fetch and format transcript
            print(f"{Fore.YELLOW}📥 Fetching transcript data...{Style.RESET_ALL}")
            transcript_data = transcript.fetch()
            
            formatter = TextFormatter()
            transcript_text = formatter.format_transcript(transcript_data)
            
            print(f"{Fore.GREEN}✅ Successfully extracted transcript{Style.RESET_ALL}")
            print(f"   Length: {len(transcript_text)} characters")
            print(f"   Language: {language}")
            print(f"   Preview: {transcript_text[:200]}...")
            
            return transcript_text
        else:
            print(f"{Fore.RED}❌ No usable transcript found{Style.RESET_ALL}")
            return None
            
    except Exception as e:
        print(f"{Fore.RED}❌ Error during transcript extraction: {str(e)}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Test transcript extraction with known working video."""
    
    # Test with the video we know has transcripts
    video_id = "fJ9rUzIMcZQ"
    
    transcript = debug_transcript_extraction(video_id)
    
    if transcript:
        print(f"\n{Fore.GREEN}🎉 Transcript extraction successful!{Style.RESET_ALL}")
        
        # Save to file for inspection
        with open("extracted_transcript.txt", "w", encoding="utf-8") as f:
            f.write(transcript)
        print(f"{Fore.BLUE}💾 Full transcript saved to 'extracted_transcript.txt'{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.RED}😞 Transcript extraction failed{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
