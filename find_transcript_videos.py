#!/usr/bin/env python3
"""
Utility to help find YouTube videos with available transcripts.
"""

from youtube_transcript_api import YouTubeTranscriptApi
from utils.validators import extract_video_id_from_url
from colorama import init, Fore, Style

# Initialize colorama
init()

def check_transcript_availability(youtube_url: str) -> bool:
    """Check if a YouTube video has available transcripts.
    
    Args:
        youtube_url: YouTube video URL
        
    Returns:
        True if transcripts are available, False otherwise
    """
    try:
        video_id = extract_video_id_from_url(youtube_url)
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        # Check for available transcripts
        available_transcripts = list(transcript_list)
        if available_transcripts:
            print(f"{Fore.GREEN}✅ Transcripts available for video {video_id}{Style.RESET_ALL}")
            
            # List available languages
            for transcript in available_transcripts:
                print(f"   - {transcript.language} ({'auto-generated' if transcript.is_generated else 'manual'})")
            
            return True
        else:
            print(f"{Fore.RED}❌ No transcripts available for video {video_id}{Style.RESET_ALL}")
            return False
            
    except Exception as e:
        print(f"{Fore.RED}❌ Error checking video {youtube_url}: {str(e)}{Style.RESET_ALL}")
        return False

def main():
    """Test some popular videos for transcript availability."""
    
    print(f"{Fore.BLUE}🔍 Checking transcript availability for popular videos...{Style.RESET_ALL}\n")
    
    # Test videos that are likely to have transcripts
    test_videos = [
        "https://www.youtube.com/watch?v=fJ9rUzIMcZQ",  # TED Talk
        "https://www.youtube.com/watch?v=UF8uR6Z6KLc",  # Popular educational
        "https://www.youtube.com/watch?v=9bZkp7q19f0",  # Popular music video
        "https://www.youtube.com/watch?v=hFZFjoX2cGg",  # Another popular video
        "https://www.youtube.com/watch?v=kJQP7kiw5Fk",  # Educational content
    ]
    
    working_videos = []
    
    for video_url in test_videos:
        if check_transcript_availability(video_url):
            working_videos.append(video_url)
        print()  # Add spacing
    
    if working_videos:
        print(f"{Fore.GREEN}🎉 Found {len(working_videos)} videos with transcripts:{Style.RESET_ALL}")
        for video in working_videos:
            print(f"   {video}")
        
        print(f"\n{Fore.YELLOW}💡 Try running the main system with one of these URLs:{Style.RESET_ALL}")
        print(f"   python3 main.py \"{working_videos[0]}\" --mood \"curious\"")
    else:
        print(f"{Fore.RED}😞 No videos with transcripts found in the test set.{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}💡 Try searching for educational videos, TED talks, or news content - they often have transcripts.{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
