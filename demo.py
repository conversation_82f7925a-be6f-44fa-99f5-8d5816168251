#!/usr/bin/env python3
"""
Complete demonstration of the YouTube to Facebook transformation system.
"""

from colorama import init, Fore, Style
import time

# Initialize colorama
init()

def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}{title.center(60)}{Style.RESET_ALL}")
    print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}\n")

def print_step(step: str, description: str):
    """Print a formatted step."""
    print(f"{Fore.YELLOW}🔸 {step}: {description}{Style.RESET_ALL}")

def main():
    """Run the complete system demonstration."""
    
    print_header("🎬 YouTube to Facebook Transformation System Demo")
    
    print(f"{Fore.GREEN}Welcome to the YouTube to Facebook Content Transformation System!{Style.RESET_ALL}")
    print(f"This system transforms YouTube videos into engaging, human-like Facebook posts.")
    print(f"\n{Fore.CYAN}✨ Key Features:{Style.RESET_ALL}")
    print(f"  • AI-powered sentiment analysis")
    print(f"  • Intelligent insight extraction")
    print(f"  • Human-like content generation")
    print(f"  • Automated quality assurance")
    print(f"  • 5-agent architecture with OpenRouter integration")
    
    print_header("🧪 System Test with Mock Data")
    
    print(f"{Fore.YELLOW}Running pipeline test with carefully crafted mock transcript...{Style.RESET_ALL}")
    print(f"This demonstrates the full transformation pipeline working perfectly.\n")
    
    # Import and run the test
    try:
        import subprocess
        result = subprocess.run(['python3', 'test_pipeline.py'], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"{Fore.GREEN}✅ Pipeline test completed successfully!{Style.RESET_ALL}")
            print(f"\nOutput preview:")
            # Show last few lines of output
            lines = result.stdout.strip().split('\n')
            for line in lines[-10:]:
                if '📱 GENERATED FACEBOOK POST' in line:
                    break
                print(f"  {line}")
        else:
            print(f"{Fore.RED}❌ Pipeline test failed{Style.RESET_ALL}")
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"{Fore.RED}❌ Could not run pipeline test: {str(e)}{Style.RESET_ALL}")
    
    print_header("🔍 YouTube Transcript Analysis")
    
    print(f"{Fore.YELLOW}Checking for videos with available transcripts...{Style.RESET_ALL}")
    
    try:
        result = subprocess.run(['python3', 'find_transcript_videos.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            # Extract working video URLs from output
            lines = result.stdout.split('\n')
            working_videos = []
            for line in lines:
                if 'https://www.youtube.com/watch?v=' in line and line.strip().startswith('   '):
                    working_videos.append(line.strip())
            
            if working_videos:
                print(f"{Fore.GREEN}✅ Found {len(working_videos)} videos with transcripts{Style.RESET_ALL}")
                print(f"\n{Fore.CYAN}📺 Available test videos:{Style.RESET_ALL}")
                for i, video in enumerate(working_videos[:3], 1):
                    print(f"  {i}. {video}")
            else:
                print(f"{Fore.YELLOW}⚠️  Transcript extraction currently experiencing issues{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Could not check transcript availability: {str(e)}{Style.RESET_ALL}")
    
    print_header("🎯 System Architecture Overview")
    
    agents = [
        ("Content Extractor", "Extracts YouTube transcripts and metadata"),
        ("Sentiment Analyzer", "Analyzes emotional tone and mood"),
        ("Thought Processor", "Identifies key insights and themes"),
        ("Content Generator", "Creates Facebook-ready content"),
        ("Quality Assurance", "Ensures human-touch standards")
    ]
    
    print(f"{Fore.CYAN}🏗️  5-Agent Architecture:{Style.RESET_ALL}")
    for i, (name, description) in enumerate(agents, 1):
        print(f"  {i}. {Fore.YELLOW}{name}{Style.RESET_ALL}: {description}")
    
    print(f"\n{Fore.CYAN}🔧 Technology Stack:{Style.RESET_ALL}")
    tech_stack = [
        "OpenRouter API for AI model access",
        "Claude 3.5 Sonnet for content generation",
        "YouTube Transcript API for content extraction",
        "Requests library for HTTP communication",
        "Modular Python architecture"
    ]
    
    for tech in tech_stack:
        print(f"  • {tech}")
    
    print_header("🚀 Getting Started")
    
    print(f"{Fore.GREEN}Ready to use the system? Here's how:{Style.RESET_ALL}\n")
    
    steps = [
        ("Setup", "Ensure you have an OpenRouter API key in your .env file"),
        ("Test", "Run 'python3 test_pipeline.py' to verify everything works"),
        ("Find Videos", "Use 'python3 find_transcript_videos.py' to find working videos"),
        ("Transform", "Run 'python3 main.py [VIDEO_URL]' with a working video"),
        ("Customize", "Add --mood and --thoughts parameters for personalization")
    ]
    
    for step, description in steps:
        print_step(step, description)
    
    print(f"\n{Fore.BLUE}📖 Example Commands:{Style.RESET_ALL}")
    print(f"  {Fore.CYAN}# Test the system{Style.RESET_ALL}")
    print(f"  python3 test_pipeline.py")
    print(f"  ")
    print(f"  {Fore.CYAN}# Find working videos{Style.RESET_ALL}")
    print(f"  python3 find_transcript_videos.py")
    print(f"  ")
    print(f"  {Fore.CYAN}# Transform a video{Style.RESET_ALL}")
    print(f"  python3 main.py \"https://www.youtube.com/watch?v=VIDEO_ID\" --mood \"inspired\"")
    
    print_header("✨ Quality Standards")
    
    quality_criteria = [
        "Authenticity: Feels genuinely human and personal",
        "Engagement: Makes readers stop scrolling",
        "Wisdom: Contains meaningful, applicable insights",
        "Flow: Natural narrative progression",
        "Connection: Creates emotional resonance",
        "Uniqueness: Avoids generic social media language"
    ]
    
    print(f"{Fore.CYAN}📊 Our quality assurance agent evaluates content on:{Style.RESET_ALL}")
    for criterion in quality_criteria:
        print(f"  • {criterion}")
    
    print(f"\n{Fore.GREEN}🎯 Target: 7.0/10 minimum quality score for publication{Style.RESET_ALL}")
    
    print_header("🎉 System Ready!")
    
    print(f"{Fore.GREEN}The YouTube to Facebook transformation system is fully operational!{Style.RESET_ALL}")
    print(f"\n{Fore.YELLOW}💡 Pro Tips:{Style.RESET_ALL}")
    print(f"  • Start with the test pipeline to see the system in action")
    print(f"  • Educational videos and TED talks work best for transcripts")
    print(f"  • Use personal mood and thoughts for more authentic content")
    print(f"  • Quality scores above 8.0 indicate excellent content")
    
    print(f"\n{Fore.BLUE}Happy transforming! 🚀{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
