"""OpenRouter API client for AI model interactions."""

import json
import time
from typing import Dict, Any, Optional
import openai
from config.settings import Settings
from utils.logger import setup_logger

logger = setup_logger(__name__)

class OpenRouterClient:
    """Client for interacting with OpenRouter API."""
    
    def __init__(self):
        """Initialize OpenRouter client."""
        Settings.validate()

        try:
            self.client = openai.OpenAI(
                base_url=Settings.OPENROUTER_BASE_URL,
                api_key=Settings.OPENROUTER_API_KEY
            )
            logger.info("OpenRouter client initialized")
        except Exception as e:
            logger.error(f"Failed to initialize OpenRouter client: {str(e)}")
            raise
    
    def generate_completion(
        self, 
        prompt: str, 
        model_key: str, 
        system_prompt: Optional[str] = None,
        max_tokens: int = 4000,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """Generate completion using specified model.
        
        Args:
            prompt: User prompt
            model_key: Key from Settings.MODELS
            system_prompt: Optional system prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            
        Returns:
            API response as dictionary
            
        Raises:
            Exception: If API call fails after retries
        """
        model = Settings.MODELS.get(model_key)
        if not model:
            raise ValueError(f"Unknown model key: {model_key}")
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        for attempt in range(Settings.MAX_RETRIES):
            try:
                logger.info(f"Calling OpenRouter API with model {model} (attempt {attempt + 1})")
                
                response = self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature
                )
                
                content = response.choices[0].message.content
                logger.info(f"Successfully received response from {model}")
                
                return {
                    "success": True,
                    "content": content,
                    "model": model,
                    "usage": response.usage.model_dump() if response.usage else None
                }
                
            except Exception as e:
                logger.warning(f"API call failed (attempt {attempt + 1}): {str(e)}")
                
                if attempt < Settings.MAX_RETRIES - 1:
                    time.sleep(Settings.RETRY_DELAY * (attempt + 1))
                else:
                    logger.error(f"All API attempts failed for model {model}")
                    raise Exception(f"OpenRouter API call failed after {Settings.MAX_RETRIES} attempts: {str(e)}")
    
    def parse_json_response(self, response_content: str) -> Dict[str, Any]:
        """Parse JSON response from API.
        
        Args:
            response_content: Raw response content
            
        Returns:
            Parsed JSON as dictionary
            
        Raises:
            ValueError: If JSON parsing fails
        """
        try:
            # Try to find JSON in the response
            content = response_content.strip()
            
            # Look for JSON block markers
            if "```json" in content:
                start = content.find("```json") + 7
                end = content.find("```", start)
                if end != -1:
                    content = content[start:end].strip()
            
            # Try to parse as JSON
            return json.loads(content)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.error(f"Raw content: {response_content}")
            raise ValueError(f"Invalid JSON response: {str(e)}")
    
    def generate_structured_response(
        self, 
        prompt: str, 
        model_key: str, 
        system_prompt: str,
        max_tokens: int = 4000
    ) -> Dict[str, Any]:
        """Generate structured JSON response.
        
        Args:
            prompt: User prompt
            model_key: Key from Settings.MODELS
            system_prompt: System prompt expecting JSON response
            max_tokens: Maximum tokens to generate
            
        Returns:
            Parsed JSON response
        """
        response = self.generate_completion(
            prompt=prompt,
            model_key=model_key,
            system_prompt=system_prompt,
            max_tokens=max_tokens,
            temperature=0.3  # Lower temperature for structured output
        )
        
        if response["success"]:
            return self.parse_json_response(response["content"])
        else:
            raise Exception("Failed to generate structured response")
