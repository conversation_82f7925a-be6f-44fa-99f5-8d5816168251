"""Input validation utilities."""

import re
from urllib.parse import urlparse, parse_qs
from config.settings import Settings

def validate_youtube_url(url: str) -> tuple[bool, str]:
    """Validate YouTube URL format and extract video ID.
    
    Args:
        url: YouTube URL to validate
        
    Returns:
        Tuple of (is_valid, video_id_or_error_message)
    """
    if not url or not isinstance(url, str):
        return False, "URL must be a non-empty string"
    
    # YouTube URL patterns
    patterns = [
        r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'(?:https?://)?(?:www\.)?youtube\.com/v/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            video_id = match.group(1)
            return True, video_id
    
    return False, "Invalid YouTube URL format"

def validate_content_length(content: str, content_type: str = "transcript") -> tuple[bool, str]:
    """Validate content length against configured limits.
    
    Args:
        content: Content to validate
        content_type: Type of content ('transcript' or 'facebook_post')
        
    Returns:
        Tuple of (is_valid, error_message_if_invalid)
    """
    if not content or not isinstance(content, str):
        return False, "Content must be a non-empty string"
    
    content_length = len(content)
    
    if content_type == "transcript":
        if content_length < Settings.MIN_TRANSCRIPT_LENGTH:
            return False, f"Transcript too short (minimum {Settings.MIN_TRANSCRIPT_LENGTH} characters)"
        if content_length > Settings.MAX_TRANSCRIPT_LENGTH:
            return False, f"Transcript too long (maximum {Settings.MAX_TRANSCRIPT_LENGTH} characters)"
    
    elif content_type == "facebook_post":
        if content_length < Settings.MIN_FACEBOOK_POST_LENGTH:
            return False, f"Facebook post too short (minimum {Settings.MIN_FACEBOOK_POST_LENGTH} characters)"
        if content_length > Settings.MAX_FACEBOOK_POST_LENGTH:
            return False, f"Facebook post too long (maximum {Settings.MAX_FACEBOOK_POST_LENGTH} characters)"
    
    return True, ""

def extract_video_id_from_url(url: str) -> str:
    """Extract video ID from YouTube URL.
    
    Args:
        url: YouTube URL
        
    Returns:
        Video ID string
        
    Raises:
        ValueError: If URL is invalid
    """
    is_valid, result = validate_youtube_url(url)
    if not is_valid:
        raise ValueError(f"Invalid YouTube URL: {result}")
    
    return result
