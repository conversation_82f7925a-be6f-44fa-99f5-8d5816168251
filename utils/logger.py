"""Logging configuration for the application."""

import logging
import sys
from config.settings import Settings

def setup_logger(name: str = __name__) -> logging.Logger:
    """Set up and configure logger for the application.
    
    Args:
        name: Logger name
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger
    
    logger.setLevel(getattr(logging, Settings.LOG_LEVEL.upper()))
    
    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(getattr(logging, Settings.LOG_LEVEL.upper()))
    
    # Create formatter
    formatter = logging.Formatter(Settings.LOG_FORMAT)
    handler.setFormatter(formatter)
    
    # Add handler to logger
    logger.addHandler(handler)
    
    return logger
