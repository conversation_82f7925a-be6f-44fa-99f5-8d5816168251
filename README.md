# YouTube to Facebook Content Transformation System

Transform YouTube video content into engaging, human-like Facebook posts through intelligent content analysis and generation.

## 🎯 Overview

This system takes YouTube URLs as input and produces thoughtful, narrative-driven social media content that resonates with readers on a personal level. It uses a sophisticated agent-based architecture with AI-powered analysis to create authentic, engaging Facebook posts.

## 🏗️ Architecture

The system consists of 5 specialized agents:

1. **Content Extractor Agent** - Extracts YouTube transcripts and metadata
2. **Sentiment Analyzer Agent** - Analyzes emotional tone and mood
3. **Thought Processor Agent** - Identifies key insights and themes
4. **Content Generator Agent** - Creates Facebook-ready content
5. **Quality Assurance Agent** - Ensures human-touch standards

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- OpenRouter API key ([Get one here](https://openrouter.ai/))

### Installation

1. **Clone or download the project**
   ```bash
   cd youtube-to-facebook
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenRouter API key
   ```

4. **Run the system**
   ```bash
   python main.py "https://www.youtube.com/watch?v=VIDEO_ID"
   ```

### Example Usage

```bash
# Test the pipeline with mock data (recommended first step)
python3 test_pipeline.py

# Find videos with available transcripts
python3 find_transcript_videos.py

# Basic usage with a working video
python3 main.py "https://www.youtube.com/watch?v=VIDEO_ID"

# With personal context
python3 main.py "https://www.youtube.com/watch?v=VIDEO_ID" \
  --mood "reflective" \
  --thoughts "This reminds me of my own journey with family"

# With quality optimization
python3 main.py "https://www.youtube.com/watch?v=VIDEO_ID" \
  --quality-attempts 3
```

## 📋 Features

### ✅ Current Features (Phase 1 MVP)

- **YouTube Content Extraction**: Automatic transcript extraction using YouTube Transcript API
- **Emotional Analysis**: AI-powered sentiment and mood analysis
- **Insight Processing**: Extraction of key wisdom and actionable takeaways
- **Content Generation**: Human-like Facebook post creation
- **Quality Assurance**: Automated quality scoring and validation
- **Command-Line Interface**: Easy-to-use CLI with colored output
- **Error Handling**: Graceful fallbacks and user-friendly error messages

### 🔄 Processing Pipeline

1. **Extract** YouTube video transcript and metadata
2. **Analyze** emotional tone, mood, and cultural context
3. **Process** key insights, wisdom, and universal themes
4. **Generate** engaging Facebook post with storytelling elements
5. **Validate** content quality against human-touch standards

## 🛠️ Configuration

### Environment Variables

Create a `.env` file with the following:

```env
# Required
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Optional: Custom model preferences
CONTENT_EXTRACTION_MODEL=anthropic/claude-3.5-sonnet
SENTIMENT_ANALYSIS_MODEL=anthropic/claude-3.5-sonnet
THOUGHT_PROCESSING_MODEL=openai/gpt-4-turbo
CONTENT_GENERATION_MODEL=anthropic/claude-3.5-sonnet
AUDIO_TRANSCRIPTION_MODEL=openai/whisper-large-v3
```

### Model Selection

The system uses different AI models optimized for each task:

- **Claude 3.5 Sonnet**: Nuanced understanding and content generation
- **GPT-4 Turbo**: Complex thought processing and analysis
- **Whisper Large V3**: Audio transcription (fallback method)

## 📊 Output Quality

The system generates content that demonstrates:

- **Personal Narrative Structure**: Opens with relatable moments
- **Emotional Depth**: Authentic human experiences
- **Practical Wisdom**: Actionable insights readers can apply
- **Cultural Sensitivity**: Respects cultural contexts
- **Conversational Tone**: Feels like talking with a friend
- **Vivid Details**: Specific imagery that brings stories to life
- **Universal Themes**: Addresses shared human experiences

## 🔧 Troubleshooting

### Common Issues

1. **"OPENROUTER_API_KEY is required"**
   - Make sure you've created a `.env` file with your API key

2. **"Invalid YouTube URL format"**
   - Ensure the URL is a valid YouTube video link
   - Supported formats: youtube.com/watch?v=, youtu.be/, youtube.com/embed/

3. **"No transcripts available for this video"**
   - YouTube transcript extraction is currently experiencing issues
   - **Recommended**: Use `python3 test_pipeline.py` to test with mock data
   - Use `python3 find_transcript_videos.py` to find videos with working transcripts
   - Try educational videos, TED talks, or news content (they often have transcripts)

4. **API rate limits or errors**
   - Check your OpenRouter account balance and limits
   - The system includes automatic retry logic

### Getting Help

- Check the logs for detailed error information
- Ensure all dependencies are installed correctly
- Verify your OpenRouter API key is valid and has sufficient credits

## 🚧 Future Enhancements

### Phase 2: Enhanced Processing
- Advanced emotion and theme detection
- Improved narrative structure
- Quality assurance automation
- Web interface

### Phase 3: Advanced Features
- Audio transcription fallback for videos without transcripts
- Multi-language support
- Custom style preferences
- Performance optimization
- Facebook API integration for direct posting

## 📝 License

This project is provided as-is for educational and personal use.

## 🤝 Contributing

This is currently a personal project. Feel free to fork and modify for your own use.

---

**Note**: This system requires an OpenRouter API key and uses AI models that may incur costs. Please monitor your usage and costs accordingly.
