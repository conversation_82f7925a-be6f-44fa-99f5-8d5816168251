#!/usr/bin/env python3
"""
Test script to verify the transformation pipeline with mock data.
"""

from agents import (
    SentimentAnalyzerAgent,
    ThoughtProcessorAgent,
    ContentGeneratorAgent,
    QualityAssuranceAgent
)
from utils.logger import setup_logger
from colorama import init, Fore, Style

# Initialize colorama for colored output
init()

logger = setup_logger(__name__)

def test_pipeline():
    """Test the transformation pipeline with mock transcript data."""
    
    # Mock transcript data (simulating a motivational/life advice video)
    mock_transcript = """
    You know, I've been thinking a lot about time lately. How it moves so fast, 
    how we often take it for granted. My father is 87 years old now, and every 
    morning he still gets up at 5 AM, makes his coffee, and sits by the window 
    watching the sunrise. 
    
    Last week when I visited him in the hospital, he told me something that 
    really stuck with me. He said, "Son, don't let yourself become like a sad 
    fish in the supermarket - all packaged up with no life left in you."
    
    It made me realize how much we rush through our days, always planning for 
    tomorrow, always waiting for the right moment to start living. But what if 
    today is all we have? What if this moment, right now, is the most important 
    one we'll ever experience?
    
    I think about my own daily habits. How I used to skip breakfast, stay up too 
    late, ignore my health. Small things, but they add up. They compound into 
    who we become. My father's morning routine isn't just about coffee and 
    sunrises - it's about choosing to be present, choosing to appreciate what's 
    in front of him.
    
    Health and family - these are the things that matter. Not the promotion, not 
    the bigger house, not the fancy car. When you're sitting in a hospital room, 
    you realize what's truly valuable. The conversations you haven't had, the 
    people you haven't called, the moments you let slip by.
    
    So maybe it's time to start that important conversation today. Maybe it's 
    time to establish those simple daily routines that will transform your life. 
    Maybe it's time to reconnect with the people who matter most.
    
    Time moves faster than we realize. Let's not waste it.
    """
    
    print(f"{Fore.BLUE}🧪 Testing transformation pipeline with mock data...{Style.RESET_ALL}")
    
    try:
        # Initialize agents
        print(f"{Fore.YELLOW}🔧 Initializing agents...{Style.RESET_ALL}")
        sentiment_analyzer = SentimentAnalyzerAgent()
        thought_processor = ThoughtProcessorAgent()
        content_generator = ContentGeneratorAgent()
        quality_assurance = QualityAssuranceAgent()
        
        # Step 1: Analyze sentiment
        print(f"{Fore.YELLOW}🎭 Analyzing sentiment...{Style.RESET_ALL}")
        sentiment_data = sentiment_analyzer.analyze_sentiment(mock_transcript)
        print(f"{Fore.GREEN}✅ Sentiment: {sentiment_data.get('primary_mood', 'unknown')} mood{Style.RESET_ALL}")
        
        # Step 2: Process thoughts
        print(f"{Fore.YELLOW}💭 Processing thoughts and insights...{Style.RESET_ALL}")
        thought_data = thought_processor.process_thoughts(mock_transcript, sentiment_data)
        insights_count = len(thought_data.get('key_insights', []))
        print(f"{Fore.GREEN}✅ Found {insights_count} key insights{Style.RESET_ALL}")
        
        # Step 3: Generate content
        print(f"{Fore.YELLOW}✍️  Generating Facebook content...{Style.RESET_ALL}")
        facebook_content = content_generator.generate_content(
            sentiment_data, 
            thought_data, 
            user_mood="reflective",
            user_thoughts="This really resonates with my own experiences"
        )
        print(f"{Fore.GREEN}✅ Content generated ({len(facebook_content)} characters){Style.RESET_ALL}")
        
        # Step 4: Quality assessment
        print(f"{Fore.YELLOW}📊 Assessing quality...{Style.RESET_ALL}")
        quality_data = quality_assurance.assess_quality(facebook_content)
        quality_score = quality_data.get('overall_score', 0)
        print(f"{Fore.GREEN}✅ Quality score: {quality_score:.1f}/10{Style.RESET_ALL}")
        
        # Display results
        print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}📱 GENERATED FACEBOOK POST{Style.RESET_ALL}")
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        
        print(f"\n{facebook_content}\n")
        
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}📊 ANALYSIS SUMMARY{Style.RESET_ALL}")
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        
        print(f"{Fore.CYAN}🎭 Sentiment: {sentiment_data.get('primary_mood', 'unknown')} ({sentiment_data.get('emotional_intensity', 0)}/10 intensity){Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Key Insights: {len(thought_data.get('key_insights', []))}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}🎯 Actionable Takeaways: {len(thought_data.get('actionable_takeaways', []))}{Style.RESET_ALL}")
        
        passes = quality_data.get('passes_quality_check', False)
        status_color = Fore.GREEN if passes else Fore.YELLOW
        print(f"{status_color}⭐ Quality Score: {quality_score:.1f}/10 {'(PASS)' if passes else '(NEEDS IMPROVEMENT)'}{Style.RESET_ALL}")
        
        print(f"\n{Fore.GREEN}🎉 Pipeline test completed successfully!{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Pipeline test failed: {str(e)}{Style.RESET_ALL}")
        logger.error(f"Pipeline test error: {str(e)}")

if __name__ == "__main__":
    test_pipeline()
