"""Sentiment Analyzer Agent for emotional tone analysis."""

from typing import Dict, Any
from utils.openrouter_client import OpenRouterClient
from config.prompts import PROMPTS
from utils.logger import setup_logger

logger = setup_logger(__name__)

class SentimentAnalyzerAgent:
    """Agent responsible for analyzing emotional tone and sentiment."""
    
    def __init__(self):
        """Initialize the Sentiment Analyzer Agent."""
        self.openrouter_client = OpenRouterClient()
        logger.info("Sentiment Analyzer Agent initialized")
    
    def analyze_sentiment(self, transcript: str) -> Dict[str, Any]:
        """Analyze emotional tone, mood, and sentiment from transcript.
        
        Args:
            transcript: Video transcript text
            
        Returns:
            Dictionary containing sentiment analysis results
        """
        try:
            logger.info("Starting sentiment analysis")
            
            # Prepare the prompt with transcript
            prompt = f"""
Please analyze the emotional content of the following transcript:

TRANSCRIPT:
{transcript}

Provide your analysis in the requested JSON format.
"""
            
            # Get structured response from OpenRouter
            response = self.openrouter_client.generate_structured_response(
                prompt=prompt,
                model_key="sentiment_analysis",
                system_prompt=PROMPTS["sentiment_analysis"],
                max_tokens=1000
            )
            
            # Validate response structure
            required_fields = ["primary_mood", "feeling_tone", "emotional_intensity", "themes", "cultural_markers"]
            for field in required_fields:
                if field not in response:
                    logger.warning(f"Missing field in sentiment analysis: {field}")
                    response[field] = self._get_default_value(field)
            
            # Validate emotional intensity is within range
            if not isinstance(response.get("emotional_intensity"), (int, float)):
                response["emotional_intensity"] = 5
            else:
                response["emotional_intensity"] = max(1, min(10, response["emotional_intensity"]))
            
            # Ensure lists are actually lists
            for list_field in ["themes", "cultural_markers"]:
                if not isinstance(response.get(list_field), list):
                    response[list_field] = []
            
            logger.info(f"Sentiment analysis completed. Primary mood: {response.get('primary_mood')}")
            return response
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
            # Return default sentiment analysis on failure
            return self._get_default_sentiment_analysis()
    
    def _get_default_value(self, field: str) -> Any:
        """Get default value for missing fields.
        
        Args:
            field: Field name
            
        Returns:
            Default value for the field
        """
        defaults = {
            "primary_mood": "neutral",
            "feeling_tone": "conversational",
            "emotional_intensity": 5,
            "themes": ["general content"],
            "cultural_markers": []
        }
        return defaults.get(field, "unknown")
    
    def _get_default_sentiment_analysis(self) -> Dict[str, Any]:
        """Get default sentiment analysis for error cases.
        
        Returns:
            Default sentiment analysis dictionary
        """
        logger.warning("Using default sentiment analysis due to processing error")
        
        return {
            "primary_mood": "neutral",
            "feeling_tone": "conversational and informative",
            "emotional_intensity": 5,
            "themes": ["informational content", "general discussion"],
            "cultural_markers": []
        }
    
    def get_sentiment_summary(self, sentiment_data: Dict[str, Any]) -> str:
        """Generate a human-readable summary of sentiment analysis.
        
        Args:
            sentiment_data: Sentiment analysis results
            
        Returns:
            Human-readable sentiment summary
        """
        mood = sentiment_data.get("primary_mood", "neutral")
        tone = sentiment_data.get("feeling_tone", "conversational")
        intensity = sentiment_data.get("emotional_intensity", 5)
        themes = sentiment_data.get("themes", [])
        
        summary = f"The content has a {mood} mood with a {tone} tone. "
        summary += f"The emotional intensity is {intensity}/10. "
        
        if themes:
            theme_text = ", ".join(themes[:3])  # Show first 3 themes
            summary += f"Key emotional themes include: {theme_text}."
        
        return summary
