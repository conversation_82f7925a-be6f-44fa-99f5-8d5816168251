"""Quality Assurance Agent for content validation."""

from typing import Dict, Any, Tuple
from utils.openrouter_client import OpenRouterClient
from config.prompts import PROMPTS
from utils.logger import setup_logger

logger = setup_logger(__name__)

class QualityAssuranceAgent:
    """Agent responsible for ensuring content meets quality standards."""
    
    def __init__(self):
        """Initialize the Quality Assurance Agent."""
        self.openrouter_client = OpenRouterClient()
        logger.info("Quality Assurance Agent initialized")
    
    def assess_quality(self, content: str) -> Dict[str, Any]:
        """Assess the quality of generated Facebook post content.
        
        Args:
            content: Generated Facebook post content
            
        Returns:
            Dictionary containing quality assessment results
        """
        try:
            logger.info("Starting quality assessment")
            
            prompt = f"""
Please evaluate the following Facebook post content against the quality criteria:

FACEBOOK POST CONTENT:
{content}

Provide a comprehensive quality assessment in the requested JSON format.
"""
            
            # Get structured response from OpenRouter
            response = self.openrouter_client.generate_structured_response(
                prompt=prompt,
                model_key="content_generation",  # Use same model as content generation
                system_prompt=PROMPTS["quality_assurance"],
                max_tokens=2000
            )
            
            # Validate and clean response
            response = self._validate_quality_response(response)
            
            logger.info(f"Quality assessment completed. Overall score: {response.get('overall_score', 0)}")
            return response
            
        except Exception as e:
            logger.error(f"Quality assessment failed: {str(e)}")
            # Return default quality assessment
            return self._get_default_quality_assessment()
    
    def _validate_quality_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean quality assessment response.
        
        Args:
            response: Raw response from AI model
            
        Returns:
            Validated and cleaned response
        """
        # Ensure overall_score exists and is valid
        if "overall_score" not in response or not isinstance(response["overall_score"], (int, float)):
            response["overall_score"] = 5.0
        else:
            response["overall_score"] = max(1.0, min(10.0, float(response["overall_score"])))
        
        # Ensure criteria_scores exists and has required structure
        if "criteria_scores" not in response or not isinstance(response["criteria_scores"], dict):
            response["criteria_scores"] = {}
        
        criteria = ["authenticity", "engagement", "wisdom", "flow", "connection", "uniqueness"]
        
        for criterion in criteria:
            if criterion not in response["criteria_scores"]:
                response["criteria_scores"][criterion] = {
                    "score": 5,
                    "explanation": "Assessment not available",
                    "suggestions": []
                }
            else:
                # Validate individual criterion structure
                criterion_data = response["criteria_scores"][criterion]
                if not isinstance(criterion_data, dict):
                    criterion_data = {"score": 5, "explanation": "Invalid data", "suggestions": []}
                
                # Validate score
                if "score" not in criterion_data or not isinstance(criterion_data["score"], (int, float)):
                    criterion_data["score"] = 5
                else:
                    criterion_data["score"] = max(1, min(10, int(criterion_data["score"])))
                
                # Validate explanation
                if "explanation" not in criterion_data or not isinstance(criterion_data["explanation"], str):
                    criterion_data["explanation"] = "No explanation provided"
                
                # Validate suggestions
                if "suggestions" not in criterion_data or not isinstance(criterion_data["suggestions"], list):
                    criterion_data["suggestions"] = []
                
                response["criteria_scores"][criterion] = criterion_data
        
        # Ensure passes_quality_check exists
        if "passes_quality_check" not in response:
            response["passes_quality_check"] = response["overall_score"] >= 7.0
        
        # Ensure overall_feedback exists
        if "overall_feedback" not in response or not isinstance(response["overall_feedback"], str):
            response["overall_feedback"] = "Quality assessment completed."
        
        return response
    
    def _get_default_quality_assessment(self) -> Dict[str, Any]:
        """Get default quality assessment for error cases.
        
        Returns:
            Default quality assessment dictionary
        """
        logger.warning("Using default quality assessment due to processing error")
        
        return {
            "overall_score": 6.0,
            "criteria_scores": {
                "authenticity": {
                    "score": 6,
                    "explanation": "Content appears reasonably authentic",
                    "suggestions": []
                },
                "engagement": {
                    "score": 6,
                    "explanation": "Content has moderate engagement potential",
                    "suggestions": []
                },
                "wisdom": {
                    "score": 6,
                    "explanation": "Contains some meaningful insights",
                    "suggestions": []
                },
                "flow": {
                    "score": 6,
                    "explanation": "Narrative flows reasonably well",
                    "suggestions": []
                },
                "connection": {
                    "score": 6,
                    "explanation": "Creates some emotional connection",
                    "suggestions": []
                },
                "uniqueness": {
                    "score": 6,
                    "explanation": "Avoids most generic language",
                    "suggestions": []
                }
            },
            "passes_quality_check": False,
            "overall_feedback": "Quality assessment could not be completed. Content may need manual review."
        }
    
    def get_quality_summary(self, quality_data: Dict[str, Any]) -> str:
        """Generate a human-readable summary of quality assessment.
        
        Args:
            quality_data: Quality assessment results
            
        Returns:
            Human-readable quality summary
        """
        overall_score = quality_data.get("overall_score", 0)
        passes_check = quality_data.get("passes_quality_check", False)
        
        summary = f"Quality Score: {overall_score:.1f}/10 "
        summary += f"({'PASS' if passes_check else 'NEEDS IMPROVEMENT'})\n\n"
        
        criteria_scores = quality_data.get("criteria_scores", {})
        for criterion, data in criteria_scores.items():
            score = data.get("score", 0)
            summary += f"• {criterion.title()}: {score}/10\n"
        
        feedback = quality_data.get("overall_feedback", "")
        if feedback:
            summary += f"\nFeedback: {feedback}"
        
        return summary
    
    def should_regenerate(self, quality_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Determine if content should be regenerated based on quality assessment.
        
        Args:
            quality_data: Quality assessment results
            
        Returns:
            Tuple of (should_regenerate, reason)
        """
        overall_score = quality_data.get("overall_score", 0)
        passes_check = quality_data.get("passes_quality_check", False)
        
        if not passes_check or overall_score < 7.0:
            # Find the lowest scoring criteria for feedback
            criteria_scores = quality_data.get("criteria_scores", {})
            lowest_criterion = min(
                criteria_scores.items(), 
                key=lambda x: x[1].get("score", 10)
            )
            
            reason = f"Quality score too low ({overall_score:.1f}/10). "
            reason += f"Weakest area: {lowest_criterion[0]} ({lowest_criterion[1].get('score', 0)}/10)"
            
            return True, reason
        
        return False, "Content meets quality standards"
