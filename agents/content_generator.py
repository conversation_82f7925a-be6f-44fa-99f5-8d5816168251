"""Content Generator Agent for creating Facebook posts."""

from typing import Dict, Any, Optional
from utils.openrouter_client import OpenRouterClient
from config.prompts import PROMPTS
from utils.validators import validate_content_length
from utils.logger import setup_logger

logger = setup_logger(__name__)

class ContentGeneratorAgent:
    """Agent responsible for generating engaging Facebook posts."""
    
    def __init__(self):
        """Initialize the Content Generator Agent."""
        self.openrouter_client = OpenRouterClient()
        logger.info("Content Generator Agent initialized")
    
    def generate_content(
        self, 
        sentiment_data: Dict[str, Any], 
        thought_data: Dict[str, Any],
        user_mood: Optional[str] = None,
        user_thoughts: Optional[str] = None
    ) -> str:
        """Generate engaging Facebook post content.
        
        Args:
            sentiment_data: Results from sentiment analysis
            thought_data: Results from thought processing
            user_mood: Optional user mood input
            user_thoughts: Optional user thoughts input
            
        Returns:
            Generated Facebook post content
        """
        try:
            logger.info("Starting content generation")
            
            # Prepare comprehensive context for content generation
            context = self._prepare_content_context(
                sentiment_data, 
                thought_data, 
                user_mood, 
                user_thoughts
            )
            
            prompt = f"""
Create a deeply engaging Facebook post using the following analysis and context:

{context}

Remember to create content that feels genuinely human and personally meaningful, 
following all the style guidelines provided in the system prompt.
"""
            
            # Generate content using OpenRouter
            response = self.openrouter_client.generate_completion(
                prompt=prompt,
                model_key="content_generation",
                system_prompt=PROMPTS["content_generation"],
                max_tokens=3000,
                temperature=0.8  # Higher creativity for content generation
            )
            
            if not response["success"]:
                raise Exception("Failed to generate content")
            
            content = response["content"].strip()
            
            # Validate content length
            is_valid, error_msg = validate_content_length(content, "facebook_post")
            if not is_valid:
                logger.warning(f"Generated content validation issue: {error_msg}")
                # Try to trim content if too long
                if len(content) > 2000:
                    content = content[:1800] + "..."
            
            logger.info(f"Content generation completed. Length: {len(content)} characters")
            return content
            
        except Exception as e:
            logger.error(f"Content generation failed: {str(e)}")
            # Return fallback content
            return self._generate_fallback_content(sentiment_data, thought_data)
    
    def _prepare_content_context(
        self, 
        sentiment_data: Dict[str, Any], 
        thought_data: Dict[str, Any],
        user_mood: Optional[str] = None,
        user_thoughts: Optional[str] = None
    ) -> str:
        """Prepare comprehensive context for content generation.
        
        Args:
            sentiment_data: Sentiment analysis results
            thought_data: Thought processing results
            user_mood: Optional user mood
            user_thoughts: Optional user thoughts
            
        Returns:
            Formatted context string
        """
        context = "CONTENT ANALYSIS:\n\n"
        
        # Sentiment context
        context += "EMOTIONAL TONE:\n"
        context += f"- Primary mood: {sentiment_data.get('primary_mood', 'neutral')}\n"
        context += f"- Feeling tone: {sentiment_data.get('feeling_tone', 'conversational')}\n"
        context += f"- Emotional intensity: {sentiment_data.get('emotional_intensity', 5)}/10\n"
        
        themes = sentiment_data.get('themes', [])
        if themes:
            context += f"- Emotional themes: {', '.join(themes)}\n"
        
        cultural_markers = sentiment_data.get('cultural_markers', [])
        if cultural_markers:
            context += f"- Cultural markers: {', '.join(cultural_markers)}\n"
        
        context += "\n"
        
        # Thought processing context
        context += "KEY INSIGHTS AND WISDOM:\n"
        
        insights = thought_data.get('key_insights', [])
        if insights:
            context += "- Key insights:\n"
            for insight in insights:
                context += f"  • {insight}\n"
        
        takeaways = thought_data.get('actionable_takeaways', [])
        if takeaways:
            context += "- Actionable takeaways:\n"
            for takeaway in takeaways:
                context += f"  • {takeaway}\n"
        
        universal_themes = thought_data.get('universal_themes', [])
        if universal_themes:
            context += f"- Universal themes: {', '.join(universal_themes)}\n"
        
        quotes = thought_data.get('memorable_quotes', [])
        if quotes:
            context += "- Memorable quotes:\n"
            for quote in quotes:
                context += f"  • \"{quote}\"\n"
        
        hooks = thought_data.get('story_hooks', [])
        if hooks:
            context += "- Story hooks:\n"
            for hook in hooks:
                context += f"  • {hook}\n"
        
        # User input context
        if user_mood or user_thoughts:
            context += "\nUSER INPUT:\n"
            if user_mood:
                context += f"- User mood: {user_mood}\n"
            if user_thoughts:
                context += f"- User thoughts: {user_thoughts}\n"
        
        return context
    
    def _generate_fallback_content(
        self, 
        sentiment_data: Dict[str, Any], 
        thought_data: Dict[str, Any]
    ) -> str:
        """Generate fallback content when main generation fails.
        
        Args:
            sentiment_data: Sentiment analysis results
            thought_data: Thought processing results
            
        Returns:
            Fallback Facebook post content
        """
        logger.warning("Using fallback content generation")
        
        mood = sentiment_data.get('primary_mood', 'thoughtful')
        insights = thought_data.get('key_insights', [])
        
        content = f"I came across something {mood} today that got me thinking.\n\n"
        
        if insights:
            content += f"The main insight that struck me: {insights[0]}\n\n"
        
        content += "Sometimes the most meaningful moments come from unexpected places. "
        content += "What we consume shapes how we see the world, and sharing these "
        content += "reflections helps us connect with each other in deeper ways.\n\n"
        content += "What's something that made you pause and think recently?"
        
        return content
