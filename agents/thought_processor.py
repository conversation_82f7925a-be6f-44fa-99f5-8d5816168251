"""Thought Processor Agent for extracting insights and themes."""

from typing import Dict, Any
from utils.openrouter_client import OpenRouterClient
from config.prompts import PROMPTS
from utils.logger import setup_logger

logger = setup_logger(__name__)

class ThoughtProcessorAgent:
    """Agent responsible for extracting key insights, wisdom, and themes."""
    
    def __init__(self):
        """Initialize the Thought Processor Agent."""
        self.openrouter_client = OpenRouterClient()
        logger.info("Thought Processor Agent initialized")
    
    def process_thoughts(self, transcript: str, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key insights, wisdom, and memorable thoughts from content.
        
        Args:
            transcript: Video transcript text
            sentiment_data: Results from sentiment analysis
            
        Returns:
            Dictionary containing processed thoughts and insights
        """
        try:
            logger.info("Starting thought processing")
            
            # Include sentiment context in the prompt
            sentiment_context = self._format_sentiment_context(sentiment_data)
            
            prompt = f"""
Please analyze the following transcript to extract meaningful insights and wisdom:

TRANSCRIPT:
{transcript}

EMOTIONAL CONTEXT:
{sentiment_context}

Focus on extracting wisdom that transcends the specific context and would resonate with a broad audience.
Provide your analysis in the requested JSON format.
"""
            
            # Get structured response from OpenRouter
            response = self.openrouter_client.generate_structured_response(
                prompt=prompt,
                model_key="thought_processing",
                system_prompt=PROMPTS["thought_processing"],
                max_tokens=2000
            )
            
            # Validate and clean response
            response = self._validate_thought_response(response)
            
            logger.info(f"Thought processing completed. Found {len(response.get('key_insights', []))} key insights")
            return response
            
        except Exception as e:
            logger.error(f"Thought processing failed: {str(e)}")
            # Return default thought analysis on failure
            return self._get_default_thought_analysis()
    
    def _format_sentiment_context(self, sentiment_data: Dict[str, Any]) -> str:
        """Format sentiment data for context in thought processing.
        
        Args:
            sentiment_data: Sentiment analysis results
            
        Returns:
            Formatted sentiment context string
        """
        mood = sentiment_data.get("primary_mood", "neutral")
        tone = sentiment_data.get("feeling_tone", "conversational")
        intensity = sentiment_data.get("emotional_intensity", 5)
        themes = sentiment_data.get("themes", [])
        
        context = f"Primary mood: {mood}\n"
        context += f"Feeling tone: {tone}\n"
        context += f"Emotional intensity: {intensity}/10\n"
        
        if themes:
            context += f"Emotional themes: {', '.join(themes)}"
        
        return context
    
    def _validate_thought_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean thought processing response.
        
        Args:
            response: Raw response from AI model
            
        Returns:
            Validated and cleaned response
        """
        required_fields = [
            "key_insights", 
            "actionable_takeaways", 
            "universal_themes", 
            "memorable_quotes", 
            "story_hooks"
        ]
        
        # Ensure all required fields exist and are lists
        for field in required_fields:
            if field not in response or not isinstance(response[field], list):
                response[field] = []
                logger.warning(f"Missing or invalid field in thought processing: {field}")
        
        # Limit list lengths to reasonable sizes
        response["key_insights"] = response["key_insights"][:5]  # Max 5 insights
        response["actionable_takeaways"] = response["actionable_takeaways"][:5]  # Max 5 takeaways
        response["universal_themes"] = response["universal_themes"][:5]  # Max 5 themes
        response["memorable_quotes"] = response["memorable_quotes"][:3]  # Max 3 quotes
        response["story_hooks"] = response["story_hooks"][:5]  # Max 5 hooks
        
        return response
    
    def _get_default_thought_analysis(self) -> Dict[str, Any]:
        """Get default thought analysis for error cases.
        
        Returns:
            Default thought analysis dictionary
        """
        logger.warning("Using default thought analysis due to processing error")
        
        return {
            "key_insights": [
                "Every piece of content contains valuable perspectives",
                "Sharing knowledge helps build connections with others"
            ],
            "actionable_takeaways": [
                "Take time to reflect on the content you consume",
                "Share meaningful insights with your community"
            ],
            "universal_themes": ["learning", "growth", "connection"],
            "memorable_quotes": [],
            "story_hooks": ["A moment of reflection", "An opportunity to learn"]
        }
    
    def get_insights_summary(self, thought_data: Dict[str, Any]) -> str:
        """Generate a human-readable summary of thought processing results.
        
        Args:
            thought_data: Thought processing results
            
        Returns:
            Human-readable insights summary
        """
        insights = thought_data.get("key_insights", [])
        takeaways = thought_data.get("actionable_takeaways", [])
        themes = thought_data.get("universal_themes", [])
        
        summary = f"Extracted {len(insights)} key insights"
        
        if takeaways:
            summary += f" and {len(takeaways)} actionable takeaways"
        
        if themes:
            theme_text = ", ".join(themes[:3])
            summary += f". Universal themes: {theme_text}"
        
        return summary + "."
