"""Content Extractor Agent for YouTube video processing."""

from typing import Dict, Any, Optional
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import TextFormatter
import pytube
from utils.openrouter_client import OpenRouterClient
from utils.validators import extract_video_id_from_url, validate_content_length
from utils.logger import setup_logger

logger = setup_logger(__name__)

class ContentExtractorAgent:
    """Agent responsible for extracting content from YouTube videos."""
    
    def __init__(self):
        """Initialize the Content Extractor Agent."""
        self.openrouter_client = OpenRouterClient()
        self.formatter = TextFormatter()
        logger.info("Content Extractor Agent initialized")
    
    def extract_content(self, youtube_url: str) -> Dict[str, Any]:
        """Extract transcript and metadata from YouTube video.
        
        Args:
            youtube_url: YouTube video URL
            
        Returns:
            Dictionary containing transcript, metadata, and extraction method
        """
        try:
            video_id = extract_video_id_from_url(youtube_url)
            logger.info(f"Extracting content for video ID: {video_id}")
            
            # Try primary method: YouTube Transcript API
            try:
                result = self._extract_via_transcript_api(video_id)
                logger.info("Successfully extracted content via Transcript API")
                return result
            except Exception as e:
                logger.warning(f"Transcript API failed: {str(e)}")
                logger.info("Falling back to audio transcription method")
                
                # Fallback method: Audio transcription
                return self._extract_via_audio_transcription(youtube_url, video_id)
                
        except Exception as e:
            logger.error(f"Content extraction failed: {str(e)}")
            raise Exception(f"Failed to extract content from YouTube video: {str(e)}")
    
    def _extract_via_transcript_api(self, video_id: str) -> Dict[str, Any]:
        """Extract content using YouTube Transcript API.
        
        Args:
            video_id: YouTube video ID
            
        Returns:
            Extracted content dictionary
        """
        # Get available transcripts
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        # Try to get English transcript first, then any available
        transcript = None
        language = "unknown"
        
        try:
            transcript = transcript_list.find_transcript(['en', 'en-US'])
            language = "English"
        except:
            # Get any available transcript
            available_transcripts = list(transcript_list)
            if available_transcripts:
                transcript = available_transcripts[0]
                language = transcript.language
        
        if not transcript:
            raise Exception("No transcripts available for this video")
        
        # Fetch and format transcript
        transcript_data = transcript.fetch()
        transcript_text = self.formatter.format_transcript(transcript_data)
        
        # Validate transcript length
        is_valid, error_msg = validate_content_length(transcript_text, "transcript")
        if not is_valid:
            raise Exception(f"Transcript validation failed: {error_msg}")
        
        # Get video metadata using pytube
        metadata = self._get_video_metadata(f"https://www.youtube.com/watch?v={video_id}")
        
        return {
            "transcript": transcript_text,
            "metadata": {
                "title": metadata.get("title", "Unknown"),
                "duration": metadata.get("duration", "Unknown"),
                "language": language
            },
            "extraction_method": "transcript_api"
        }
    
    def _extract_via_audio_transcription(self, youtube_url: str, video_id: str) -> Dict[str, Any]:
        """Extract content via audio transcription (fallback method).
        
        Args:
            youtube_url: YouTube video URL
            video_id: YouTube video ID
            
        Returns:
            Extracted content dictionary
        """
        logger.info("Starting audio transcription process")
        
        # Get video metadata first
        metadata = self._get_video_metadata(youtube_url)
        
        # For now, return a placeholder since audio transcription requires
        # additional setup and file handling
        # In a full implementation, this would:
        # 1. Download audio using pytube
        # 2. Convert to appropriate format
        # 3. Send to OpenRouter Whisper model
        # 4. Return transcribed text
        
        raise Exception(
            "Audio transcription fallback not yet implemented. "
            "This video does not have available transcripts. "
            "Please try a different video with existing transcripts."
        )
    
    def _get_video_metadata(self, youtube_url: str) -> Dict[str, Any]:
        """Get video metadata using pytube.
        
        Args:
            youtube_url: YouTube video URL
            
        Returns:
            Video metadata dictionary
        """
        try:
            yt = pytube.YouTube(youtube_url)
            
            return {
                "title": yt.title or "Unknown Title",
                "duration": str(yt.length) + " seconds" if yt.length else "Unknown",
                "description": yt.description or "",
                "author": yt.author or "Unknown",
                "views": yt.views or 0
            }
        except Exception as e:
            logger.warning(f"Failed to get metadata: {str(e)}")
            return {
                "title": "Unknown Title",
                "duration": "Unknown",
                "description": "",
                "author": "Unknown",
                "views": 0
            }
