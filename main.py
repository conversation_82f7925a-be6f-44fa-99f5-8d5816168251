#!/usr/bin/env python3
"""
YouTube to Facebook Content Transformation System

Main orchestrator that coordinates all agents to transform YouTube videos
into engaging Facebook posts.
"""

import sys
import argparse
from typing import Dict, Any, Optional
from colorama import init, Fore, Style

from agents import (
    ContentExtractorAgent,
    SentimentAnalyzerAgent,
    ThoughtProcessorAgent,
    ContentGeneratorAgent,
    QualityAssuranceAgent
)
from utils.logger import setup_logger
from config.settings import Settings

# Initialize colorama for colored output
init()

logger = setup_logger(__name__)

class YouTubeToFacebookTransformer:
    """Main orchestrator for the YouTube to Facebook transformation pipeline."""
    
    def __init__(self):
        """Initialize the transformation system."""
        logger.info("Initializing YouTube to Facebook Transformer")
        
        # Initialize all agents
        self.content_extractor = ContentExtractorAgent()
        self.sentiment_analyzer = SentimentAnalyzerAgent()
        self.thought_processor = ThoughtProcessorAgent()
        self.content_generator = ContentGeneratorAgent()
        self.quality_assurance = QualityAssuranceAgent()
        
        logger.info("All agents initialized successfully")
    
    def transform(
        self, 
        youtube_url: str, 
        user_mood: Optional[str] = None,
        user_thoughts: Optional[str] = None,
        max_quality_attempts: int = 2
    ) -> Dict[str, Any]:
        """Transform YouTube video into Facebook post.
        
        Args:
            youtube_url: YouTube video URL
            user_mood: Optional user mood input
            user_thoughts: Optional user thoughts input
            max_quality_attempts: Maximum attempts for quality improvement
            
        Returns:
            Dictionary containing transformation results
        """
        try:
            print(f"{Fore.BLUE}🎬 Starting YouTube to Facebook transformation...{Style.RESET_ALL}")
            
            # Step 1: Extract content
            print(f"{Fore.YELLOW}📝 Extracting content from YouTube video...{Style.RESET_ALL}")
            content_data = self.content_extractor.extract_content(youtube_url)
            print(f"{Fore.GREEN}✅ Content extracted via {content_data['extraction_method']}{Style.RESET_ALL}")
            
            # Step 2: Analyze sentiment
            print(f"{Fore.YELLOW}🎭 Analyzing emotional tone and sentiment...{Style.RESET_ALL}")
            sentiment_data = self.sentiment_analyzer.analyze_sentiment(content_data['transcript'])
            print(f"{Fore.GREEN}✅ Sentiment analysis complete: {sentiment_data['primary_mood']} mood{Style.RESET_ALL}")
            
            # Step 3: Process thoughts
            print(f"{Fore.YELLOW}💭 Processing insights and themes...{Style.RESET_ALL}")
            thought_data = self.thought_processor.process_thoughts(
                content_data['transcript'], 
                sentiment_data
            )
            insights_count = len(thought_data.get('key_insights', []))
            print(f"{Fore.GREEN}✅ Thought processing complete: {insights_count} key insights found{Style.RESET_ALL}")
            
            # Step 4: Generate content (with quality loop)
            print(f"{Fore.YELLOW}✍️  Generating Facebook post content...{Style.RESET_ALL}")
            
            best_content = None
            best_quality_score = 0
            
            for attempt in range(max_quality_attempts):
                # Generate content
                facebook_content = self.content_generator.generate_content(
                    sentiment_data, 
                    thought_data, 
                    user_mood, 
                    user_thoughts
                )
                
                # Assess quality
                quality_data = self.quality_assurance.assess_quality(facebook_content)
                quality_score = quality_data.get('overall_score', 0)
                
                print(f"{Fore.CYAN}📊 Quality assessment (attempt {attempt + 1}): {quality_score:.1f}/10{Style.RESET_ALL}")
                
                # Keep the best version
                if quality_score > best_quality_score:
                    best_content = facebook_content
                    best_quality_score = quality_score
                    best_quality_data = quality_data
                
                # If quality is good enough, stop
                if quality_data.get('passes_quality_check', False):
                    break
            
            print(f"{Fore.GREEN}✅ Content generation complete with quality score: {best_quality_score:.1f}/10{Style.RESET_ALL}")
            
            # Compile results
            results = {
                'success': True,
                'youtube_url': youtube_url,
                'video_metadata': content_data['metadata'],
                'extraction_method': content_data['extraction_method'],
                'sentiment_analysis': sentiment_data,
                'thought_processing': thought_data,
                'facebook_content': best_content,
                'quality_assessment': best_quality_data,
                'user_inputs': {
                    'mood': user_mood,
                    'thoughts': user_thoughts
                }
            }
            
            print(f"{Fore.GREEN}🎉 Transformation completed successfully!{Style.RESET_ALL}")
            return results
            
        except Exception as e:
            error_msg = f"Transformation failed: {str(e)}"
            logger.error(error_msg)
            print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
            
            return {
                'success': False,
                'error': error_msg,
                'youtube_url': youtube_url
            }
    
    def display_results(self, results: Dict[str, Any]) -> None:
        """Display transformation results in a user-friendly format.
        
        Args:
            results: Transformation results dictionary
        """
        if not results.get('success', False):
            print(f"{Fore.RED}❌ Transformation failed: {results.get('error', 'Unknown error')}{Style.RESET_ALL}")
            return
        
        print(f"\n{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}📱 FACEBOOK POST CONTENT{Style.RESET_ALL}")
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        
        print(f"\n{results['facebook_content']}\n")
        
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.BLUE}📊 ANALYSIS SUMMARY{Style.RESET_ALL}")
        print(f"{Fore.BLUE}{'='*60}{Style.RESET_ALL}")
        
        # Video info
        metadata = results.get('video_metadata', {})
        print(f"{Fore.CYAN}🎬 Video: {metadata.get('title', 'Unknown')}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}⏱️  Duration: {metadata.get('duration', 'Unknown')}{Style.RESET_ALL}")
        
        # Sentiment summary
        sentiment = results.get('sentiment_analysis', {})
        print(f"{Fore.CYAN}🎭 Mood: {sentiment.get('primary_mood', 'Unknown')} ({sentiment.get('emotional_intensity', 0)}/10 intensity){Style.RESET_ALL}")
        
        # Quality score
        quality = results.get('quality_assessment', {})
        quality_score = quality.get('overall_score', 0)
        passes = quality.get('passes_quality_check', False)
        status_color = Fore.GREEN if passes else Fore.YELLOW
        print(f"{status_color}⭐ Quality Score: {quality_score:.1f}/10 {'(PASS)' if passes else '(NEEDS IMPROVEMENT)'}{Style.RESET_ALL}")

def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="Transform YouTube videos into engaging Facebook posts"
    )
    parser.add_argument(
        "youtube_url", 
        help="YouTube video URL to transform"
    )
    parser.add_argument(
        "--mood", 
        help="Your current mood or desired tone for the post"
    )
    parser.add_argument(
        "--thoughts", 
        help="Your personal thoughts or perspective to include"
    )
    parser.add_argument(
        "--quality-attempts", 
        type=int, 
        default=2,
        help="Maximum attempts to improve content quality (default: 2)"
    )
    
    args = parser.parse_args()
    
    try:
        # Validate settings
        Settings.validate()
        
        # Initialize transformer
        transformer = YouTubeToFacebookTransformer()
        
        # Run transformation
        results = transformer.transform(
            youtube_url=args.youtube_url,
            user_mood=args.mood,
            user_thoughts=args.thoughts,
            max_quality_attempts=args.quality_attempts
        )
        
        # Display results
        transformer.display_results(results)
        
        # Exit with appropriate code
        sys.exit(0 if results.get('success', False) else 1)
        
    except Exception as e:
        print(f"{Fore.RED}❌ System error: {str(e)}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == "__main__":
    main()
