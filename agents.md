# YouTube to Facebook Content Transformation Agent System

## Project Overview

This project transforms YouTube video content into engaging, human-like Facebook posts through intelligent content analysis and generation. The system takes YouTube URLs as input and produces thoughtful, narrative-driven social media content that resonates with readers on a personal level.

## Architecture

### Core Components

1. **Content Extractor Agent** - Handles YouTube content retrieval
2. **Sentiment Analyzer Agent** - Extracts emotional tone and mood
3. **Thought Processor Agent** - Identifies key insights and themes  
4. **Content Generator Agent** - Creates Facebook-ready content
5. **Quality Assurance Agent** - Ensures output meets human-touch standards

## Agent Specifications

### 1. Content Extractor Agent

**Purpose**: Extract transcript and metadata from YouTube videos

**Capabilities**:
- Extract YouTube video transcripts using `youtube-transcript-api`
- Fallback audio transcription via OpenRouter for videos without existing transcripts
- Retrieve video metadata (title, description, duration)
- Handle multiple language transcripts

**Implementation Strategy**:
- Primary: Use `YouTubeTranscriptApi` for efficiency (most YouTube videos have auto-generated transcripts)
- Secondary: For videos without transcripts, extract audio using `pytube` and transcribe via OpenRouter's audio-capable models (Whisper-compatible models)

**OpenRouter Models**: 
- Audio transcription: Models with audio input capabilities (search for "audio" modality on OpenRouter)
- Content processing: Claude, GPT, or Mistral models

**Input**: YouTube URL
**Output**: 
```python
{
    "transcript": "full video transcript text",
    "metadata": {
        "title": "video title",
        "duration": "video length",
        "language": "detected language"
    },
    "extraction_method": "transcript_api" | "audio_transcription"
}
```

### 2. Sentiment Analyzer Agent

**Purpose**: Extract emotional tone, mood, and sentiment from content

**Capabilities**:
- Analyze transcript for emotional undertones
- Identify primary mood (reflective, inspirational, melancholic, hopeful, etc.)
- Extract feeling tone (warm, serious, contemplative, urgent, etc.)
- Detect cultural and contextual emotional markers

**OpenRouter Implementation**:
```python
# Use Claude or GPT models for nuanced emotional analysis
system_prompt = """
You are an expert at analyzing emotional undertones in text. 
Analyze the following transcript and extract:
1. Primary mood (single word)
2. Feeling tone (descriptive phrase)
3. Emotional intensity (1-10 scale)
4. Key emotional themes present
5. Cultural/contextual emotional markers

Be precise and nuanced in your analysis.
"""
```

**Input**: Video transcript
**Output**:
```python
{
    "primary_mood": "reflective",
    "feeling_tone": "contemplative with underlying warmth", 
    "emotional_intensity": 7,
    "themes": ["life lessons", "regret", "family bonds", "time appreciation"],
    "cultural_markers": ["Thai family dynamics", "respect for elders"]
}
```

### 3. Thought Processor Agent

**Purpose**: Extract key insights, wisdom, and memorable thoughts from content

**Capabilities**:
- Identify profound statements and life lessons
- Extract actionable insights
- Find relatable universal themes
- Distill complex ideas into digestible thoughts
- Recognize storytelling elements and narrative hooks

**OpenRouter Implementation**:
```python
system_prompt = """
You are a master at extracting meaningful insights from content.
From this transcript, identify:
1. Top 3-5 most profound insights or life lessons
2. Actionable takeaways readers can apply
3. Universal themes that resonate across cultures  
4. Memorable quotes or statements (if any)
5. Story elements that create emotional connection

Focus on wisdom that transcends the specific context.
"""
```

**Input**: Video transcript + sentiment analysis
**Output**:
```python
{
    "key_insights": [
        "Time moves faster than we realize - cherish present moments",
        "Health and family matter more than material pursuits",
        "Simple daily habits compound into life transformation"
    ],
    "actionable_takeaways": [
        "Start that important conversation today",
        "Establish simple daily health routines",
        "Reconnect with people you haven't spoken to"
    ],
    "universal_themes": ["mortality awareness", "relationship priorities", "health wisdom"],
    "memorable_quotes": ["Don't let yourself become like a sad fish in the supermarket"],
    "story_hooks": ["87-year-old father's morning routine", "hospital visits that changed perspective"]
}
```

### 4. Content Generator Agent

**Purpose**: Transform extracted content into engaging Facebook posts

**Capabilities**:
- Create human-like, conversational narrative flow
- Weave personal anecdotes with universal wisdom
- Structure content for social media engagement
- Maintain authentic voice and tone
- Generate compelling opening hooks and thoughtful conclusions

**OpenRouter Implementation**:
```python
system_prompt = """
You are a master storyteller who creates deeply engaging social media content.

Using the provided content analysis, create a Facebook post that:
1. Opens with a compelling personal moment or observation
2. Weaves in the key insights naturally through storytelling
3. Uses conversational, warm tone that feels like talking to a close friend
4. Includes specific, vivid details that bring the story to life
5. Ends with gentle reflection or invitation for connection
6. Maintains authenticity and avoids social media clichés
7. Uses minimal to no emojis
8. Creates emotional resonance through genuine human experience

Style guidelines:
- Write as if sharing a meaningful conversation over coffee
- Use natural paragraph breaks for readability
- Include specific details that make the story vivid
- Balance wisdom with humility
- Create connection through shared human experience
- Length: 500-1500 words (engaging but substantial)
"""
```

**Input**: All previous agent outputs + user preferences
**Output**: Polished Facebook-ready content

### 5. Quality Assurance Agent

**Purpose**: Ensure output meets "super human-touch" standards

**Capabilities**:
- Verify emotional authenticity and resonance
- Check narrative flow and engagement potential
- Ensure cultural sensitivity and appropriateness
- Validate against provided examples' quality standards
- Optimize for Facebook best practices

**Quality Criteria**:
- Authenticity: Does it feel genuinely human and personal?
- Engagement: Would readers stop scrolling to read this?
- Wisdom: Are insights meaningful and applicable?
- Flow: Does the narrative progress naturally?
- Connection: Does it create emotional resonance?
- Uniqueness: Avoids generic social media language

## Integration Flow

```mermaid
graph TD
    A[YouTube URL Input] --> B[Content Extractor Agent]
    B --> C[Sentiment Analyzer Agent] 
    B --> D[Thought Processor Agent]
    C --> E[Content Generator Agent]
    D --> E
    E --> F[Quality Assurance Agent]
    F --> G[Final Facebook Content]
    
    H[User Mood Input] --> E
    I[User Thoughts Input] --> E
```

## OpenRouter API Integration

### Model Selection Strategy
- **Primary Models**: Claude 3.5 Sonnet or GPT-4 for nuanced understanding
- **Alternative Models**: Mistral Large for cost-effective processing
- **Audio Models**: Whisper-compatible models for transcription when needed

### API Configuration
```python
import openai

# Configure OpenRouter client
client = openai.OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="your-openrouter-api-key"
)

# Model selection based on task complexity
MODELS = {
    "content_extraction": "anthropic/claude-3.5-sonnet",
    "sentiment_analysis": "anthropic/claude-3.5-sonnet", 
    "thought_processing": "openai/gpt-4-turbo",
    "content_generation": "anthropic/claude-3.5-sonnet",
    "audio_transcription": "openai/whisper-large-v3"  # if needed
}
```

### Cost Optimization
- Use efficient models for simpler tasks
- Implement response caching for repeated content
- Batch process multiple requests when possible
- Monitor token usage and optimize prompts

## Technical Implementation

### Dependencies
```python
# Core dependencies
youtube-transcript-api==0.6.1
pytube==15.0.0
openai==1.3.0
requests==2.31.0

# Optional for audio processing
pydub==0.25.1
```

### Error Handling Strategy
- Graceful fallback between transcript methods
- Retry logic for API failures
- User-friendly error messages
- Logging for debugging and monitoring

### Input Validation
- YouTube URL format validation
- Transcript availability checking
- Content length limits
- Language detection and support

## Output Standards

Based on the provided examples, the output should demonstrate:

1. **Personal Narrative Structure**: Opens with specific, relatable moments
2. **Emotional Depth**: Explores feelings and human experiences authentically  
3. **Practical Wisdom**: Includes actionable insights readers can apply
4. **Cultural Sensitivity**: Respects cultural contexts and family dynamics
5. **Conversational Tone**: Feels like talking with a thoughtful friend
6. **Vivid Details**: Uses specific imagery that brings stories to life
7. **Universal Themes**: Addresses experiences most humans can relate to
8. **Gentle Conclusion**: Ends with reflection or gentle invitation

## Success Metrics

- **Engagement Potential**: Content that would make readers pause and reflect
- **Authenticity Score**: Feels genuinely human, not AI-generated
- **Wisdom Value**: Provides meaningful insights readers remember
- **Emotional Resonance**: Creates genuine connection with audience
- **Shareability**: Content people would want to share with friends/family

## Future Enhancements

- Multi-language support for global content
- Integration with Facebook API for direct posting
- A/B testing capabilities for content variations
- Analytics integration for engagement tracking
- Custom style templates for different content types

## Development Phases

### Phase 1: Core MVP
- Basic YouTube transcript extraction
- Simple sentiment analysis
- Basic content generation using OpenRouter
- Command-line interface

### Phase 2: Enhanced Processing  
- Advanced emotion and theme detection
- Improved narrative structure
- Quality assurance automation
- Web interface

### Phase 3: Advanced Features
- Audio transcription fallback
- Multi-language support
- Custom style preferences
- Performance optimization

This agent system creates a sophisticated pipeline that transforms YouTube content into deeply engaging Facebook posts while maintaining the authentic, human-touch quality demonstrated in your examples.